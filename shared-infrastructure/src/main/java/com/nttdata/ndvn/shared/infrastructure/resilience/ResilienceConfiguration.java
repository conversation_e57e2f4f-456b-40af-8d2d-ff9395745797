package com.nttdata.ndvn.shared.infrastructure.resilience;

import io.github.resilience4j.bulkhead.Bulkhead;
import io.github.resilience4j.bulkhead.BulkheadConfig;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.IntervalFunction;
import io.github.resilience4j.timelimiter.TimeLimiter;
import io.github.resilience4j.timelimiter.TimeLimiterConfig;
import io.micrometer.core.instrument.MeterRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Resilience4j configuration for service-to-service communication.
 * 
 * Provides:
 * - Circuit breaker patterns for fault tolerance
 * - Retry mechanisms with exponential backoff
 * - Time limiters for request timeouts
 * - Bulkhead patterns for resource isolation
 * - Service-specific configurations
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
@Configuration
public class ResilienceConfiguration {
    
    private static final Logger logger = LoggerFactory.getLogger(ResilienceConfiguration.class);
    
    private final MeterRegistry meterRegistry;
    private final Map<String, CircuitBreaker> circuitBreakers = new ConcurrentHashMap<>();
    private final Map<String, Retry> retries = new ConcurrentHashMap<>();
    private final Map<String, TimeLimiter> timeLimiters = new ConcurrentHashMap<>();
    private final Map<String, Bulkhead> bulkheads = new ConcurrentHashMap<>();
    
    public ResilienceConfiguration(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }
    
    /**
     * Get or create a circuit breaker for a service.
     * 
     * @param serviceName the name of the service
     * @return configured circuit breaker
     */
    public CircuitBreaker getCircuitBreaker(String serviceName) {
        return circuitBreakers.computeIfAbsent(serviceName, this::createCircuitBreaker);
    }
    
    /**
     * Get or create a retry mechanism for a service.
     * 
     * @param serviceName the name of the service
     * @return configured retry
     */
    public Retry getRetry(String serviceName) {
        return retries.computeIfAbsent(serviceName, this::createRetry);
    }
    
    /**
     * Get or create a time limiter for a service.
     * 
     * @param serviceName the name of the service
     * @return configured time limiter
     */
    public TimeLimiter getTimeLimiter(String serviceName) {
        return timeLimiters.computeIfAbsent(serviceName, this::createTimeLimiter);
    }
    
    /**
     * Get or create a bulkhead for a service.
     * 
     * @param serviceName the name of the service
     * @return configured bulkhead
     */
    public Bulkhead getBulkhead(String serviceName) {
        return bulkheads.computeIfAbsent(serviceName, this::createBulkhead);
    }
    
    /**
     * Create a circuit breaker for a service.
     */
    private CircuitBreaker createCircuitBreaker(String serviceName) {
        CircuitBreakerConfig config = getCircuitBreakerConfig(serviceName);
        CircuitBreaker circuitBreaker = CircuitBreaker.of(serviceName + "-circuit-breaker", config);
        
        // Register metrics - TODO: Implement proper metrics registration
        
        // Add event listeners
        circuitBreaker.getEventPublisher()
                .onStateTransition(event -> 
                        logger.info("Circuit breaker {} state transition: {} -> {}", 
                                serviceName, event.getStateTransition().getFromState(), 
                                event.getStateTransition().getToState()))
                .onFailureRateExceeded(event -> 
                        logger.warn("Circuit breaker {} failure rate exceeded: {}%", 
                                serviceName, event.getFailureRate()))
                .onCallNotPermitted(event -> 
                        logger.warn("Circuit breaker {} call not permitted", serviceName));
        
        logger.info("Created circuit breaker for service: {}", serviceName);
        return circuitBreaker;
    }
    
    /**
     * Create a retry mechanism for a service.
     */
    private Retry createRetry(String serviceName) {
        RetryConfig config = getRetryConfig(serviceName);
        Retry retry = Retry.of(serviceName + "-retry", config);
        
        // Register metrics - TODO: Implement proper metrics registration
        
        // Add event listeners
        retry.getEventPublisher()
                .onRetry(event -> 
                        logger.debug("Retry {} attempt {} for service {}", 
                                event.getNumberOfRetryAttempts(), event.getNumberOfRetryAttempts(), serviceName))
                .onError(event -> 
                        logger.warn("Retry {} failed for service {} after {} attempts", 
                                event.getName(), serviceName, event.getNumberOfRetryAttempts()));
        
        logger.info("Created retry for service: {}", serviceName);
        return retry;
    }
    
    /**
     * Create a time limiter for a service.
     */
    private TimeLimiter createTimeLimiter(String serviceName) {
        TimeLimiterConfig config = getTimeLimiterConfig(serviceName);
        TimeLimiter timeLimiter = TimeLimiter.of(serviceName + "-time-limiter", config);
        
        // Register metrics - TODO: Implement proper metrics registration
        
        logger.info("Created time limiter for service: {}", serviceName);
        return timeLimiter;
    }
    
    /**
     * Create a bulkhead for a service.
     */
    private Bulkhead createBulkhead(String serviceName) {
        BulkheadConfig config = getBulkheadConfig(serviceName);
        Bulkhead bulkhead = Bulkhead.of(serviceName + "-bulkhead", config);
        
        // Register metrics - TODO: Implement proper metrics registration
        
        // Add event listeners
        bulkhead.getEventPublisher()
                .onCallPermitted(event -> 
                        logger.debug("Bulkhead {} call permitted", serviceName))
                .onCallRejected(event -> 
                        logger.warn("Bulkhead {} call rejected", serviceName));
        
        logger.info("Created bulkhead for service: {}", serviceName);
        return bulkhead;
    }
    
    /**
     * Get circuit breaker configuration for a service.
     */
    private CircuitBreakerConfig getCircuitBreakerConfig(String serviceName) {
        return CircuitBreakerConfig.custom()
                .failureRateThreshold(getFailureRateThreshold(serviceName))
                .waitDurationInOpenState(getWaitDurationInOpenState(serviceName))
                .slidingWindowSize(getSlidingWindowSize(serviceName))
                .minimumNumberOfCalls(getMinimumNumberOfCalls(serviceName))
                .permittedNumberOfCallsInHalfOpenState(getPermittedCallsInHalfOpen(serviceName))
                .automaticTransitionFromOpenToHalfOpenEnabled(true)
                .build();
    }
    
    /**
     * Get retry configuration for a service.
     */
    private RetryConfig getRetryConfig(String serviceName) {
        return RetryConfig.custom()
                .maxAttempts(getMaxRetryAttempts(serviceName))
                .waitDuration(getRetryWaitDuration(serviceName))
                .intervalFunction(IntervalFunction.ofExponentialBackoff(getRetryWaitDuration(serviceName), getRetryBackoffMultiplier(serviceName)))
                .retryOnException(throwable -> isRetryableException(throwable))
                .build();
    }
    
    /**
     * Get time limiter configuration for a service.
     */
    private TimeLimiterConfig getTimeLimiterConfig(String serviceName) {
        return TimeLimiterConfig.custom()
                .timeoutDuration(getTimeoutDuration(serviceName))
                .cancelRunningFuture(true)
                .build();
    }
    
    /**
     * Get bulkhead configuration for a service.
     */
    private BulkheadConfig getBulkheadConfig(String serviceName) {
        return BulkheadConfig.custom()
                .maxConcurrentCalls(getMaxConcurrentCalls(serviceName))
                .maxWaitDuration(getBulkheadMaxWaitDuration(serviceName))
                .build();
    }
    
    // Service-specific configuration methods with defaults
    
    private float getFailureRateThreshold(String serviceName) {
        return switch (serviceName) {
            case "user-management-service" -> 60.0f;
            case "customer-management-service" -> 50.0f;
            case "product-catalog-service" -> 70.0f;
            case "order-management-service" -> 40.0f;
            case "notification-service" -> 80.0f;
            default -> 50.0f;
        };
    }
    
    private Duration getWaitDurationInOpenState(String serviceName) {
        return switch (serviceName) {
            case "user-management-service" -> Duration.ofSeconds(30);
            case "customer-management-service" -> Duration.ofSeconds(30);
            case "product-catalog-service" -> Duration.ofSeconds(20);
            case "order-management-service" -> Duration.ofSeconds(60);
            case "notification-service" -> Duration.ofSeconds(10);
            default -> Duration.ofSeconds(30);
        };
    }
    
    private int getSlidingWindowSize(String serviceName) {
        return switch (serviceName) {
            case "order-management-service" -> 20;
            default -> 10;
        };
    }
    
    private int getMinimumNumberOfCalls(String serviceName) {
        return switch (serviceName) {
            case "order-management-service" -> 10;
            default -> 5;
        };
    }
    
    private int getPermittedCallsInHalfOpen(String serviceName) {
        return 3;
    }
    
    private int getMaxRetryAttempts(String serviceName) {
        return switch (serviceName) {
            case "notification-service" -> 5;
            case "order-management-service" -> 2;
            default -> 3;
        };
    }
    
    private Duration getRetryWaitDuration(String serviceName) {
        return switch (serviceName) {
            case "notification-service" -> Duration.ofMillis(500);
            case "order-management-service" -> Duration.ofSeconds(2);
            default -> Duration.ofSeconds(1);
        };
    }
    
    private double getRetryBackoffMultiplier(String serviceName) {
        return 2.0;
    }
    
    private Duration getTimeoutDuration(String serviceName) {
        return switch (serviceName) {
            case "user-management-service" -> Duration.ofSeconds(5);
            case "customer-management-service" -> Duration.ofSeconds(5);
            case "product-catalog-service" -> Duration.ofSeconds(10);
            case "order-management-service" -> Duration.ofSeconds(30);
            case "notification-service" -> Duration.ofSeconds(15);
            default -> Duration.ofSeconds(10);
        };
    }
    
    private int getMaxConcurrentCalls(String serviceName) {
        return switch (serviceName) {
            case "order-management-service" -> 5;
            case "notification-service" -> 20;
            default -> 10;
        };
    }
    
    private Duration getBulkheadMaxWaitDuration(String serviceName) {
        return Duration.ofMillis(500);
    }
    
    /**
     * Determine if an exception is retryable.
     */
    private boolean isRetryableException(Throwable throwable) {
        // Retry on network errors, timeouts, and 5xx server errors
        return throwable instanceof java.net.ConnectException ||
               throwable instanceof java.net.SocketTimeoutException ||
               throwable instanceof java.util.concurrent.TimeoutException ||
               (throwable instanceof org.springframework.web.reactive.function.client.WebClientResponseException webEx &&
                webEx.getStatusCode().is5xxServerError());
    }
}
