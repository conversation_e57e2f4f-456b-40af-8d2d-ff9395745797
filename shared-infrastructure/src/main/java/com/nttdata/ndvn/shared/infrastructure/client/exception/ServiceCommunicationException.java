package com.nttdata.ndvn.shared.infrastructure.client.exception;

/**
 * Base exception for service-to-service communication errors.
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2025-06-28
 */
public class ServiceCommunicationException extends RuntimeException {
    
    public ServiceCommunicationException(String message) {
        super(message);
    }
    
    public ServiceCommunicationException(String message, Throwable cause) {
        super(message, cause);
    }
}

/**
 * Exception thrown when a service is not found.
 */
class ServiceNotFoundException extends ServiceCommunicationException {

    public ServiceNotFoundException(String message) {
        super(message);
    }
}

/**
 * Exception thrown when authentication fails.
 */
class ServiceAuthenticationException extends ServiceCommunicationException {

    public ServiceAuthenticationException(String message) {
        super(message);
    }
}

/**
 * Exception thrown when authorization fails.
 */
class ServiceAuthorizationException extends ServiceCommunicationException {

    public ServiceAuthorizationException(String message) {
        super(message);
    }
}

/**
 * Exception thrown when validation fails.
 */
class ServiceValidationException extends ServiceCommunicationException {

    public ServiceValidationException(String message) {
        super(message);
    }
}

/**
 * Exception thrown when a service has internal errors.
 */
class ServiceInternalException extends ServiceCommunicationException {

    public ServiceInternalException(String message) {
        super(message);
    }
}

/**
 * Exception thrown when a service is unavailable.
 */
class ServiceUnavailableException extends ServiceCommunicationException {

    public ServiceUnavailableException(String message) {
        super(message);
    }
}
